package io.hydrax.pricestreaming.router;

import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.exception.RejectionException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class RoutingEngine {
  private final Supplier<List<Rule>> rules;

  public void route(Order order, List<String> venueCodes) {
    // Optimized: Cache venue codes as Set for faster lookups
    Set<String> venueSet =
        venueCodes instanceof Set ? (Set<String>) venueCodes : new HashSet<>(venueCodes);

    // Optimized: Get rules once and reuse
    List<Rule> rulesList = rules.get();

    if (log.isTraceEnabled()) {
      // Cache psOrder reference for logging
      var psOrder = order.getPsOrder();
      rulesList.forEach(
          rule ->
              log.trace(
                  "rule: {}, \\n timeinforce: {}, \\n orderType: {}, \\n ticker: {}",
                  rule,
                  psOrder.getTimeInForce(),
                  psOrder.getOrdType(),
                  psOrder.getSymbol()));
    }

    // Optimized: Find matching rule with early termination and efficient venue checking
    Rule matchedRule = null;
    for (Rule rule : rulesList) {
      if (rule.match(order)) {
        // Optimized: Check venue intersection more efficiently
        List<String> ruleVenues = rule.getVenueMarkets();
        boolean hasCommonVenue = false;
        for (String venue : ruleVenues) {
          if (venueSet.contains(venue)) {
            hasCommonVenue = true;
            break;
          }
        }
        if (hasCommonVenue) {
          matchedRule = rule;
          break;
        }
      }
    }

    if (matchedRule == null) {
      throw new RejectionException("no matched rule");
    }

    log.trace("matched rule: {}", matchedRule);

    // Optimized: Build final venue list more efficiently
    List<String> finalVenues = new ArrayList<>();
    List<String> ruleVenues = matchedRule.getVenueMarkets();
    for (String venue : ruleVenues) {
      if (venueSet.contains(venue)) {
        finalVenues.add(venue);
      }
    }

    matchedRule.handle(order, finalVenues);
  }
}
