package io.hydrax.pricestreaming.events;

import io.hydrax.aeron.AeronMessage;
import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.common.Topic;
import io.hydrax.pricestreaming.cache.*;
import io.hydrax.pricestreaming.common.OrderType;
import io.hydrax.pricestreaming.common.RoutingStrategyType;
import io.hydrax.pricestreaming.common.TimeInForceEnum;
import io.hydrax.pricestreaming.config.MarketModelServiceFactory;
import io.hydrax.pricestreaming.domain.*;
import io.hydrax.pricestreaming.router.*;
import io.hydrax.pricestreaming.service.*;
import io.hydrax.pricestreaming.utils.TopicUtil;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.zip.CRC32;
import org.agrona.DirectBuffer;
import org.agrona.concurrent.UnsafeBuffer;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

/**
 * 完整链路的 JMH 基准测试，包括 mock aeronCluster.offer 和 publication.offer
 *
 * <p>这个测试专门用于测试从 OrderEvent.onOrder 到 ClientManager 内部方法的完整调用链路
 *
 * <p>Run with: ./gradlew jmh --include=".*OrderEventFullChainBenchmark.*"
 */
@BenchmarkMode({Mode.AverageTime, Mode.Throughput})
@OutputTimeUnit(TimeUnit.SECONDS)
@State(Scope.Benchmark)
@Fork(
    value = 1,
    jvmArgs = {"-Xms2G", "-Xmx2G", "-XX:+UseG1GC"})
@Warmup(iterations = 3, time = 2, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 3, timeUnit = TimeUnit.SECONDS)
public class OrderEventFullChainBenchmark {

  private ManualOrderEvent orderEvent;
  private ManualOrderService orderService;
  private ManualClientManager clientManager;

  // Manual implementations (replacing Mockito mocks)
  private ManualAeronCluster manualAeronCluster;
  private ManualExclusivePublication manualPublication;

  // 测试数据
  private Order newOrderMessage;
  private Order cancelOrderMessage;
  private Order marketOrderMessage;

  // 性能计数器
  private final AtomicLong offerCallCount = new AtomicLong(0);
  private final AtomicLong publicationCallCount = new AtomicLong(0);
  private final AtomicLong totalLatency = new AtomicLong(0);
  private final AtomicLong totalSerializationTime = new AtomicLong(0);
  private final AtomicLong totalCompressionTime = new AtomicLong(0);
  private final AtomicLong totalNetworkTime = new AtomicLong(0);

  // 序列化统计
  private final AtomicLong totalSerializedBytes = new AtomicLong(0);
  private final AtomicLong totalCompressedBytes = new AtomicLong(0);

  @Setup(Level.Trial)
  public void setupTrial() {
    setupManualAeronComponents();
    setupManualClientManager();
    setupManualOrderService();

    orderEvent = new ManualOrderEvent(orderService, clientManager);
    createTestData();
  }

  /** 设置手动实现的 Aeron 组件 */
  private void setupManualAeronComponents() {
    // 创建手动实现的 AeronCluster
    manualAeronCluster = new ManualAeronCluster(offerCallCount, totalLatency);

    // 创建手动实现的 ExclusivePublication
    manualPublication = new ManualExclusivePublication(publicationCallCount, totalLatency);
  }

  /** 设置手动实现的 ClientManager */
  private void setupManualClientManager() {
    clientManager =
        new ManualClientManager(
            manualAeronCluster, manualPublication, totalLatency, totalSerializationTime, this);
  }

  /** 设置手动实现的 OrderService */
  private void setupManualOrderService() {
    orderService = new ManualOrderService(clientManager, this);
  }

  /** 模拟 ClientManager.send 内部调用 aeronCluster.offer（增强版） */
  private Object simulateClientManagerSend(Object message) {
    long startTime = System.nanoTime();

    // 1. 模拟消息序列化（包含真实的 Protobuf + Aeron 头部）
    DirectBuffer serializedMessage = simulateMessageSerialization(message);

    // 4. 调用手动实现的 aeronCluster.offer
    long position = manualAeronCluster.offer(serializedMessage, 0, serializedMessage.capacity());

    long endTime = System.nanoTime();
    totalLatency.addAndGet(endTime - startTime);

    return position;
  }

  /** 模拟 ClientManager.sendToOwn 内部调用 publication.offer（增强版） */
  private Object simulateClientManagerSendToOwn(Object message) {
    long startTime = System.nanoTime();

    // 1. 模拟消息序列化（本地发送通常不需要网络头部）
    DirectBuffer serializedMessage = simulateMessageSerialization(message);

    // 4. 调用手动实现的 publication.offer
    long position = manualPublication.offer(serializedMessage, 0, serializedMessage.capacity());

    long endTime = System.nanoTime();
    totalLatency.addAndGet(endTime - startTime);

    return position;
  }

  /** 模拟本地进程间通信延迟 */
  private void simulateLocalIPCLatency() {
    // 本地 IPC 延迟通常在 100ns - 2μs 之间
    long ipcLatency = 100 + ThreadLocalRandom.current().nextLong(1900);

    // 偶尔的调度延迟（1%概率）
    if (ThreadLocalRandom.current().nextDouble() < 0.01) {
      ipcLatency += 1000 + ThreadLocalRandom.current().nextLong(5000); // 1-6μs
    }

    busyWait(ipcLatency);
  }

  /** 模拟 OrderService 处理逻辑 */
  private void simulateOrderServiceProcessing(Order order) {
    // 模拟调用 clientManager.send（发送执行报告）
    clientManager.send(createMockExecutionReport(order));
  }

  /** 模拟取消订单处理逻辑 */
  private void simulateCancelOrderProcessing(PsOrder order) {
    // 模拟调用 clientManager.send（发送取消确认）
    clientManager.send(createMockCancelConfirmation(order));
  }

  /** 模拟真实的消息序列化场景 */
  private DirectBuffer simulateMessageSerialization(Object message) {
    long startTime = System.nanoTime();

    // 1. 模拟 Protobuf 序列化
    byte[] protobufBytes = simulateProtobufSerialization(message);

    // 4. 记录序列化统计
    long endTime = System.nanoTime();
    totalSerializationTime.addAndGet(endTime - startTime);
    totalSerializedBytes.addAndGet(protobufBytes.length);

    return new UnsafeBuffer(protobufBytes);
  }

  /** 使用真实的 Protobuf 序列化 */
  private byte[] simulateProtobufSerialization(Object message) {
    long startTime = System.nanoTime();

    byte[] serializedData;

    try {
      if (message instanceof ERResponseList erResponseList) {
        if (erResponseList.getResponseList() != null) {
          // 使用真实的 protobuf toByteArray()
          serializedData = erResponseList.getResponseList().toByteArray();
        } else {
          // 创建一个默认的 ResponseList 进行序列化
          ResponseList defaultResponseList = createDefaultResponseList();
          serializedData = defaultResponseList.toByteArray();
        }
      } else if (message instanceof Order order) {
        // 使用真实的 PsOrder protobuf 序列化
        serializedData = order.getPsOrder().toByteArray();
      } else if (message instanceof PsOrder psOrder) {
        // 直接序列化 PsOrder
        serializedData = psOrder.toByteArray();
      } else if (message instanceof ResponseList responseList) {
        serializedData = responseList.toByteArray();
      } else {
        // 对于其他类型，创建一个包装的 protobuf 消息
        serializedData = createGenericProtobufMessage(message).toByteArray();
      }
    } catch (Exception e) {
      // 如果序列化失败，使用备用方法
      serializedData = message.toString().getBytes();
    }

    long endTime = System.nanoTime();
    totalSerializationTime.addAndGet(endTime - startTime);

    return serializedData;
  }

  /** 创建默认的 ResponseList 用于测试 */
  private ResponseList createDefaultResponseList() {
    return ResponseList.newBuilder()
        .setOutSequence(System.nanoTime())
        .setFromService("sor")
        .addResponses(
            Response.newBuilder()
                .setPsParentOrderExecReport(
                    PsParentOrderExecReport.newBuilder()
                        .setOrderId("TEST_ORDER_" + System.nanoTime())
                        .setServiceAccountId("TEST_ACCOUNT")
                        .setUserId("TEST_USER")
                        .setOrderStatus(PsParentOrderStatus.PS_PARENT_ORDER_STATUS_PENDING)
                        // .setTransactTime(System.currentTimeMillis()) // 如果字段不存在则注释掉
                        .build())
                .build())
        .build();
  }

  /** 创建默认的 PsChildOrderExecReport 用于测试 */
  private Response createDefaultChildOrderResponse() {
    return Response.newBuilder()
        .setPsChildOrderExecReport(
            PsChildOrderExecReport.newBuilder()
                .setOrderId("CHILD_ORDER_" + System.nanoTime())
                .setParentOrderId("PARENT_ORDER_" + System.nanoTime())
                .setServiceAccountId("TEST_ACCOUNT")
                .setUserId("TEST_USER")
                .setOrderStatus(PsChildOrderStatus.PS_CHILD_ORDER_STATUS_PENDING)
                .setVenueCode("TEST_VENUE")
                .setVenueSymbol("BTC/USD")
                .setTransactTime(System.currentTimeMillis())
                .build())
        .build();
  }

  /** 为通用对象创建 protobuf 包装 */
  private ResponseList createGenericProtobufMessage(Object message) {
    // 将任意对象包装成一个简单的 protobuf 消息
    String messageStr = message.toString();

    return ResponseList.newBuilder()
        .setOutSequence(System.nanoTime())
        .setFromService("generic")
        .addResponses(
            Response.newBuilder()
                .setPsParentOrderExecReport(
                    PsParentOrderExecReport.newBuilder()
                        .setOrderId(
                            messageStr.length() > 50 ? messageStr.substring(0, 50) : messageStr)
                        .setServiceAccountId("GENERIC")
                        .setUserId("SYSTEM")
                        .setOrderStatus(PsParentOrderStatus.PS_PARENT_ORDER_STATUS_PENDING)
                        // .setTransactTime(System.currentTimeMillis()) // 如果字段不存在则注释掉
                        .build())
                .build())
        .build();
  }

  /** 添加 Aeron 消息头部 */
  private byte[] addAeronMessageHeader(byte[] protobufData) {
    // Aeron 消息格式：[Header][Payload]
    // Header: [Length:4][Type:4][Timestamp:8][Checksum:4]
    ByteBuffer buffer = ByteBuffer.allocate(protobufData.length + 20);

    // Message length (不包括这个字段本身)
    buffer.putInt(protobufData.length + 16);

    // Message type (自定义)
    buffer.putInt(0x1001); // ORDER_MESSAGE_TYPE

    // Timestamp
    buffer.putLong(System.nanoTime());

    // Checksum (CRC32)
    CRC32 crc32 = new CRC32();
    crc32.update(protobufData);
    buffer.putInt((int) crc32.getValue());

    // Payload
    buffer.put(protobufData);

    return buffer.array();
  }

  /** 模拟压缩（可选） */
  private byte[] simulateCompression(byte[] data) {
    // 模拟简单的压缩算法
    if (data.length > 100) {
      long startTime = System.nanoTime();

      // 模拟压缩处理时间（基于数据大小）
      simulateCompressionProcessing(data.length);

      // 模拟压缩比（通常 60-80%）
      int compressedSize =
          (int) (data.length * (0.6 + ThreadLocalRandom.current().nextDouble(0.2)));
      byte[] compressed = new byte[compressedSize + 4]; // +4 for compression header

      // 压缩头部：原始大小
      ByteBuffer.wrap(compressed).putInt(data.length);

      // 模拟压缩数据（实际上是随机数据，但大小正确）
      // 填充压缩数据（模拟）
      byte[] randomData = new byte[compressedSize];
      ThreadLocalRandom.current().nextBytes(randomData);
      System.arraycopy(randomData, 0, compressed, 4, compressedSize);

      long endTime = System.nanoTime();
      totalCompressionTime.addAndGet(endTime - startTime);
      totalCompressedBytes.addAndGet(compressed.length);

      return compressed;
    }

    return data; // 小消息不压缩
  }

  /** 辅助方法：写入 varint */
  private void putVarInt(ByteBuffer buffer, long value) {
    while ((value & 0xFFFFFFFFFFFFFF80L) != 0L) {
      buffer.put((byte) ((value & 0x7F) | 0x80));
      value >>>= 7;
    }
    buffer.put((byte) (value & 0x7F));
  }

  /** 模拟 Protobuf 处理的 CPU 开销 */
  private void simulateProtobufProcessing(int dataSize) {
    // Protobuf 序列化的 CPU 开销主要来自：
    // 1. 字段编码
    // 2. Varint 编码
    // 3. 内存分配和复制

    // 基础开销：500ns
    long baseLatency = 500;

    // 数据大小相关开销：每字节 2ns
    long sizeLatency = dataSize * 2L;

    // 字段数量相关开销（估算）
    long fieldLatency = (dataSize / 20) * 50L; // 假设平均每20字节一个字段，每字段50ns

    long totalLatency = baseLatency + sizeLatency + fieldLatency;
    busyWait(totalLatency);
  }

  /** 模拟压缩处理的 CPU 开销 */
  private void simulateCompressionProcessing(int dataSize) {
    // 压缩算法的 CPU 开销（如 LZ4, Snappy）
    // 通常比序列化慢 2-5 倍

    // 基础开销：1μs
    long baseLatency = 1000;

    // 数据大小相关开销：每字节 5ns
    long sizeLatency = dataSize * 5L;

    long totalLatency = baseLatency + sizeLatency;
    busyWait(totalLatency);
  }

  /** 模拟网络传输延迟（更真实的模型） */
  private void simulateRealisticNetworkLatency(int messageSize) {
    long startTime = System.nanoTime();

    // 网络延迟模型：
    // 1. 基础延迟（RTT的一半）：100-500μs
    long baseLatency = 100000 + ThreadLocalRandom.current().nextLong(400000);

    // 2. 传输时间（基于消息大小和带宽）
    // 假设 1Gbps 网络 = 125MB/s = 125 bytes/μs
    long transmissionTime = messageSize / 125; // μs

    // 3. 网络抖动：±10%
    double jitterFactor = 0.9 + ThreadLocalRandom.current().nextDouble(0.2);
    long totalLatency = (long) ((baseLatency + transmissionTime) * jitterFactor);

    // 4. 偶尔的网络拥塞（5%概率延迟增加2-5倍）
    if (ThreadLocalRandom.current().nextDouble() < 0.05) {
      totalLatency *= (2 + ThreadLocalRandom.current().nextInt(4));
    }

    busyWait(totalLatency);

    long endTime = System.nanoTime();
    totalNetworkTime.addAndGet(endTime - startTime);
  }

  /** 模拟内存分配和 GC 压力 */
  private void simulateMemoryAllocationOverhead(int allocationSize) {
    // 大对象分配的开销
    if (allocationSize > 1024) {
      // 大对象分配：额外 100-500ns
      long allocationLatency = 100 + ThreadLocalRandom.current().nextLong(400);
      busyWait(allocationLatency);
    }

    // 模拟 GC 压力（偶尔触发）
    if (ThreadLocalRandom.current().nextDouble() < 0.01) { // 1% 概率
      // 模拟 minor GC：1-10ms
      long gcLatency = 1000000 + ThreadLocalRandom.current().nextLong(9000000);
      busyWait(gcLatency);
    }
  }

  /** 忙等待指定的纳秒数 */
  private void busyWait(long nanos) {
    long start = System.nanoTime();
    while (System.nanoTime() - start < nanos) {
      // 忙等待
    }
  }

  /** 创建模拟的执行报告 */
  private ERResponseList createMockExecutionReport(Order order) {
    ERResponseList erResponseList = new ERResponseList();

    // 创建真实的 ResponseList 用于序列化测试
    ResponseList responseList =
        ResponseList.newBuilder()
            .setOutSequence(System.nanoTime())
            .setFromService("sor")
            .addResponses(
                Response.newBuilder()
                    .setPsParentOrderExecReport(
                        PsParentOrderExecReport.newBuilder()
                            .setOrderId(order.getPsOrder().getClOrdId())
                            .setServiceAccountId(order.getPsOrder().getServiceAccountId())
                            .setUserId(order.getPsOrder().getUserId())
                            .setOrderStatus(PsParentOrderStatus.PS_PARENT_ORDER_STATUS_CONFIRMED)
                            // .setTransactTime(System.currentTimeMillis()) // 如果字段不存在则注释掉
                            .build())
                    .build())
            .build();

    erResponseList.setResponseList(responseList);
    return erResponseList;
  }

  /** 创建模拟的取消确认 */
  private ERResponseList createMockCancelConfirmation(PsOrder order) {
    ERResponseList erResponseList = new ERResponseList();

    // 创建真实的 ResponseList 用于序列化测试
    ResponseList responseList =
        ResponseList.newBuilder()
            .setOutSequence(System.nanoTime())
            .setFromService("sor")
            .addResponses(
                Response.newBuilder()
                    .setPsParentOrderExecReport(
                        PsParentOrderExecReport.newBuilder()
                            .setOrderId(order.getClOrdId())
                            .setServiceAccountId(order.getServiceAccountId())
                            .setUserId(order.getUserId())
                            .setOrderStatus(
                                PsParentOrderStatus.PS_PARENT_ORDER_STATUS_PENDING) // 使用存在的状态
                            // .setTransactTime(System.currentTimeMillis()) // 如果字段不存在则注释掉
                            .build())
                    .build())
            .build();

    erResponseList.setResponseList(responseList);
    return erResponseList;
  }

  /** 创建测试数据 */
  private void createTestData() {
    // 创建新订单消息
    PsOrder newOrder =
        PsOrder.newBuilder()
            .setClOrdId("NEW_ORDER_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from(BigDecimal.valueOf(100.0)))
            .setPrice(UDec128Util.from(BigDecimal.valueOf(50000.0)))
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setServiceAccountId("SERVICE_123")
            .setUserId("USER_123")
            .build();
    newOrderMessage = createMockMessage(newOrder);

    // 创建取消订单消息
    PsOrder cancelOrder =
        PsOrder.newBuilder()
            .setClOrdId("CANCEL_ORDER_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setRequestType(RequestType.REQUEST_TYPE_CANCEL_ORDER)
            .setOrderId("ORDER_123")
            .setUserId("USER_123")
            .build();
    cancelOrderMessage = createMockMessage(cancelOrder);

    // 创建市场订单消息
    PsOrder marketOrder =
        PsOrder.newBuilder()
            .setClOrdId("MARKET_ORDER_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_MARKET)
            .setQty(UDec128Util.from(BigDecimal.valueOf(100.0)))
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setServiceAccountId("SERVICE_123")
            .setUserId("USER_123")
            .build();
    marketOrderMessage = createMockMessage(marketOrder);
  }

  @SuppressWarnings("unchecked")
  private Order createMockMessage(PsOrder psOrder) {
    Order order = Order.builder().psOrder(psOrder).startSequence(System.nanoTime()).build();
    return order;
  }

  // ========== 完整链路基准测试 ==========
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void onOrderNewOrderFullChain(Blackhole bh) {
  //    try {
  //      orderEvent.onOrder(newOrderMessage);
  //      bh.consume(offerCallCount.get());
  //    } catch (Exception e) {
  //      bh.consume(e);
  //    }
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void onOrderCancelOrderFullChain(Blackhole bh) {
  //    try {
  //      orderEvent.onOrder(cancelOrderMessage);
  //      bh.consume(offerCallCount.get());
  //    } catch (Exception e) {
  //      bh.consume(e);
  //    }
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void onOrderMarketOrderFullChain(Blackhole bh) {
  //    try {
  //      orderEvent.onOrder(marketOrderMessage);
  //      bh.consume(offerCallCount.get());
  //    } catch (Exception e) {
  //      bh.consume(e);
  //    }
  //  }
  //
  //  // ========== Aeron 组件性能测试 ==========
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void aeronClusterOfferOnly(Blackhole bh) {
  //    DirectBuffer testMessage = new UnsafeBuffer("test message".getBytes());
  //    long position = manualAeronCluster.offer(testMessage, 0, testMessage.capacity());
  //    bh.consume(position);
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void publicationOfferOnly(Blackhole bh) {
  //    DirectBuffer testMessage = new UnsafeBuffer("test message".getBytes());
  //    long position = manualPublication.offer(testMessage, 0, testMessage.capacity());
  //    bh.consume(position);
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void clientManagerSendOnly(Blackhole bh) {
  //    ERResponseList testMessage = new ERResponseList();
  //    Object result = simulateClientManagerSend(testMessage);
  //    bh.consume(result);
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void clientManagerSendToOwnOnly(Blackhole bh) {
  //    ERResponseList testMessage = new ERResponseList();
  //    Object result = simulateClientManagerSendToOwn(testMessage);
  //    bh.consume(result);
  //  }

  // ========== 吞吐量测试 ==========

  @Benchmark
  @BenchmarkMode(Mode.Throughput)
  @OperationsPerInvocation(100)
  public void onOrderFullChainThroughput(Blackhole bh) {
    for (int i = 0; i < 100; i++) {
      try {
        Order message;
        switch (i % 3) {
          case 0:
            message = newOrderMessage;
            break;
          case 1:
            message = cancelOrderMessage;
            break;
          default:
            message = marketOrderMessage;
            break;
        }
        orderEvent.onOrder(message);
      } catch (Exception e) {
        bh.consume(e);
      }
    }
    bh.consume(offerCallCount.get());
    bh.consume(publicationCallCount.get());
  }

  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.Throughput)
  //  @OperationsPerInvocation(500)
  //  public void aeronClusterOfferThroughput(Blackhole bh) {
  //    DirectBuffer testMessage = new UnsafeBuffer("throughput test message".getBytes());
  //    for (int i = 0; i < 500; i++) {
  //      long position = manualAeronCluster.offer(testMessage, 0, testMessage.capacity());
  //      bh.consume(position);
  //    }
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.Throughput)
  //  @OperationsPerInvocation(1000)
  //  public void publicationOfferThroughput(Blackhole bh) {
  //    DirectBuffer testMessage = new UnsafeBuffer("throughput test message".getBytes());
  //    for (int i = 0; i < 1000; i++) {
  //      long position = manualPublication.offer(testMessage, 0, testMessage.capacity());
  //      bh.consume(position);
  //    }
  //  }
  //
  //  // ========== 并发测试 ==========
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.Throughput)
  //  @Threads(4)
  //  @OperationsPerInvocation(50)
  //  public void onOrderFullChainConcurrent(Blackhole bh) {
  //    for (int i = 0; i < 50; i++) {
  //      try {
  //        Order message = (i % 2 == 0) ? newOrderMessage : marketOrderMessage;
  //        orderEvent.onOrder(message);
  //      } catch (Exception e) {
  //        bh.consume(e);
  //      }
  //    }
  //    bh.consume(offerCallCount.get());
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.Throughput)
  //  @Threads(8)
  //  @OperationsPerInvocation(25)
  //  public void aeronClusterOfferConcurrent(Blackhole bh) {
  //    DirectBuffer testMessage = new UnsafeBuffer("concurrent test message".getBytes());
  //    for (int i = 0; i < 25; i++) {
  //      long position = manualAeronCluster.offer(testMessage, 0, testMessage.capacity());
  //      bh.consume(position);
  //    }
  //  }
  //
  //  // ========== 性能分析测试 ==========
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void onOrderWithMetrics(Blackhole bh) {
  //    long startTime = System.nanoTime();
  //
  //    try {
  //      orderEvent.onOrder(newOrderMessage);
  //    } catch (Exception e) {
  //      bh.consume(e);
  //    }
  //
  //    long endTime = System.nanoTime();
  //    long totalTime = endTime - startTime;
  //
  //    bh.consume(totalTime);
  //    bh.consume(offerCallCount.get());
  //    bh.consume(publicationCallCount.get());
  //    bh.consume(totalLatency.get());
  //  }
  //
  //  // ========== 清理方法 ==========
  //
  //  // ========== 序列化性能测试 ==========
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void protobufSerializationOnly(Blackhole bh) {
  //    // 创建一个真实的 ERResponseList 进行序列化测试
  //    ERResponseList testMessage = new ERResponseList();
  //    testMessage.setResponseList(createDefaultResponseList());
  //
  //    byte[] serialized = simulateProtobufSerialization(testMessage);
  //    bh.consume(serialized);
  //    bh.consume(totalSerializationTime.get());
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void orderSerializationOnly(Blackhole bh) {
  //    // 直接使用 PsOrder 的 toByteArray() 方法
  //    PsOrder order = newOrderMessage.getPsOrder();
  //    long startTime = System.nanoTime();
  //    byte[] serialized = order.toByteArray();
  //    long endTime = System.nanoTime();
  //
  //    totalSerializationTime.addAndGet(endTime - startTime);
  //    bh.consume(serialized);
  //    bh.consume(totalSerializationTime.get());
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void responseListSerializationOnly(Blackhole bh) {
  //    // 测试 ResponseList 的序列化性能
  //    ResponseList responseList = createDefaultResponseList();
  //    long startTime = System.nanoTime();
  //    byte[] serialized = responseList.toByteArray();
  //    long endTime = System.nanoTime();
  //
  //    totalSerializationTime.addAndGet(endTime - startTime);
  //    bh.consume(serialized);
  //    bh.consume(serialized.length);
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void smallProtobufMessageSerialization(Blackhole bh) {
  //    // 测试小消息的序列化性能
  //    PsOrder smallOrder =
  //        PsOrder.newBuilder()
  //            .setClOrdId("SMALL_" + System.nanoTime())
  //            .setSymbol("BTC")
  //            .setSide(Side.SIDE_BUY)
  //            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
  //            .build();
  //
  //    long startTime = System.nanoTime();
  //    byte[] serialized = smallOrder.toByteArray();
  //    long endTime = System.nanoTime();
  //
  //    totalSerializationTime.addAndGet(endTime - startTime);
  //    bh.consume(serialized);
  //    bh.consume(serialized.length);
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void largeProtobufMessageSerialization(Blackhole bh) {
  //    // 测试大消息的序列化性能
  //    ResponseList.Builder builder =
  //        ResponseList.newBuilder()
  //            .setOutSequence(System.nanoTime())
  //            .setFromService("performance_test_service_with_long_name");
  //
  //    // 添加多个响应来增加消息大小
  //    for (int i = 0; i < 10; i++) {
  //      builder.addResponses(
  //          Response.newBuilder()
  //              .setPsParentOrderExecReport(
  //                  PsParentOrderExecReport.newBuilder()
  //                      .setOrderId("LARGE_ORDER_" + i + "_" + System.nanoTime())
  //                      .setServiceAccountId("LARGE_ACCOUNT_ID_" + i)
  //                      .setUserId("LARGE_USER_ID_" + i)
  //                      .setOrderStatus(PsParentOrderStatus.PS_PARENT_ORDER_STATUS_PENDING) //
  // 使用存在的状态
  //                      // .setTransactTime(System.currentTimeMillis()) // 如果字段不存在则注释掉
  //                      .build())
  //              .build());
  //    }
  //
  //    ResponseList largeResponseList = builder.build();
  //
  //    long startTime = System.nanoTime();
  //    byte[] serialized = largeResponseList.toByteArray();
  //    long endTime = System.nanoTime();
  //
  //    totalSerializationTime.addAndGet(endTime - startTime);
  //    bh.consume(serialized);
  //    bh.consume(serialized.length);
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void complexOrderSerialization(Blackhole bh) {
  //    // 测试复杂订单的序列化性能
  //    PsOrder complexOrder =
  //        PsOrder.newBuilder()
  //            .setClOrdId("COMPLEX_ORDER_" + System.nanoTime())
  //            .setSymbol("BTCUSDT")
  //            .setSide(Side.SIDE_BUY)
  //            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
  //            .setQty(UDec128Util.from(BigDecimal.valueOf(123.456789)))
  //            .setPrice(UDec128Util.from(BigDecimal.valueOf(50000.123456)))
  //            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
  //            .setServiceAccountId("COMPLEX_SERVICE_ACCOUNT_ID_WITH_LONG_NAME")
  //            .setUserId("COMPLEX_USER_ID_WITH_LONG_NAME")
  //            // .setTimeInForce(TimeInForce.TIME_IN_FORCE_GTC) // 如果枚举值不存在则注释掉
  //            .setOrderId("COMPLEX_ORDER_ID_" + System.nanoTime())
  //            .build();
  //
  //    long startTime = System.nanoTime();
  //    byte[] serialized = complexOrder.toByteArray();
  //    long endTime = System.nanoTime();
  //
  //    totalSerializationTime.addAndGet(endTime - startTime);
  //    bh.consume(serialized);
  //    bh.consume(serialized.length);
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void aeronMessageHeaderOnly(Blackhole bh) {
  //    byte[] protobufData = "test message".getBytes();
  //    byte[] withHeader = addAeronMessageHeader(protobufData);
  //    bh.consume(withHeader);
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void compressionOnly(Blackhole bh) {
  //    byte[] testData = new byte[1024];
  //    ThreadLocalRandom.current().nextBytes(testData);
  //    byte[] compressed = simulateCompression(testData);
  //    bh.consume(compressed);
  //    bh.consume(totalCompressionTime.get());
  //  }
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.Throughput)
  //  @OperationsPerInvocation(100)
  //  public void serializationThroughput(Blackhole bh) {
  //    for (int i = 0; i < 100; i++) {
  //      Object message = (i % 2 == 0) ? new ERResponseList() : newOrderMessage;
  //      DirectBuffer serialized = simulateMessageSerialization(message);
  //      bh.consume(serialized);
  //    }
  //    bh.consume(totalSerializationTime.get());
  //    bh.consume(totalSerializedBytes.get());
  //  }
  //
  //  // ========== 性能分析和统计 ==========
  //
  //  @Benchmark
  //  @BenchmarkMode(Mode.AverageTime)
  //  public void fullChainWithDetailedMetrics(Blackhole bh) {
  //    // 重置计数器
  //    long startSerializationTime = totalSerializationTime.get();
  //    long startCompressionTime = totalCompressionTime.get();
  //    long startNetworkTime = totalNetworkTime.get();
  //    long startSerializedBytes = totalSerializedBytes.get();
  //
  //    try {
  //      orderEvent.onOrder(newOrderMessage);
  //    } catch (Exception e) {
  //      bh.consume(e);
  //    }
  //
  //    // 计算增量
  //    long serializationDelta = totalSerializationTime.get() - startSerializationTime;
  //    long compressionDelta = totalCompressionTime.get() - startCompressionTime;
  //    long networkDelta = totalNetworkTime.get() - startNetworkTime;
  //    long bytesDelta = totalSerializedBytes.get() - startSerializedBytes;
  //
  //    bh.consume(serializationDelta);
  //    bh.consume(compressionDelta);
  //    bh.consume(networkDelta);
  //    bh.consume(bytesDelta);
  //    bh.consume(offerCallCount.get());
  //  }

  @TearDown(Level.Iteration)
  public void resetCounters() {
    offerCallCount.set(0);
    publicationCallCount.set(0);
    totalLatency.set(0);
    totalSerializationTime.set(0);
    totalCompressionTime.set(0);
    totalNetworkTime.set(0);
    totalSerializedBytes.set(0);
    totalCompressedBytes.set(0);
  }

  @TearDown(Level.Trial)
  public void printDetailedStats() {
    System.out.println("\n=== 详细性能统计 ===");
    System.out.println("总序列化时间: " + totalSerializationTime.get() + " ns");
    System.out.println("总压缩时间: " + totalCompressionTime.get() + " ns");
    System.out.println("总网络时间: " + totalNetworkTime.get() + " ns");
    System.out.println("总序列化字节数: " + totalSerializedBytes.get() + " bytes");
    System.out.println("总压缩字节数: " + totalCompressedBytes.get() + " bytes");

    if (totalSerializedBytes.get() > 0 && totalCompressedBytes.get() > 0) {
      double compressionRatio = (double) totalCompressedBytes.get() / totalSerializedBytes.get();
      System.out.println("压缩比: " + String.format("%.2f%%", compressionRatio * 100));
    }

    if (totalSerializedBytes.get() > 0 && totalSerializationTime.get() > 0) {
      double throughput =
          (double) totalSerializedBytes.get() / (totalSerializationTime.get() / 1_000_000_000.0);
      System.out.println("序列化吞吐量: " + String.format("%.2f MB/s", throughput / 1_000_000));
    }
  }

  // ========== 手动实现类（替代 Mockito） ==========

  /** 手动实现的 OrderEvent，替代原始的 OrderEvent */
  private static class ManualOrderEvent {
    private final ManualOrderService orderService;
    private final ManualClientManager clientManager;

    public ManualOrderEvent(ManualOrderService orderService, ManualClientManager clientManager) {
      this.orderService = orderService;
      this.clientManager = clientManager;
    }

    public void onOrder(Order order) {
      // 模拟 OrderEvent.onOrder 的逻辑
      switch (order.getPsOrder().getRequestType()) {
        case REQUEST_TYPE_NEW_ORDER:
          orderService.placeOrder(order);
          break;
        case REQUEST_TYPE_EDIT_ORDER:
          // 编辑订单逻辑（暂时跳过）
          break;
        case REQUEST_TYPE_CANCEL_ORDER:
          orderService.cancelOrder(order.getPsOrder());
          break;
        default:
          // 未知订单类型
          break;
      }
    }
  }

  /** 手动实现的 AeronCluster，替代 Mockito mock */
  private static class ManualAeronCluster {
    private final AtomicLong offerCallCount;
    private final AtomicLong totalLatency;

    public ManualAeronCluster(AtomicLong offerCallCount, AtomicLong totalLatency) {
      this.offerCallCount = offerCallCount;
      this.totalLatency = totalLatency;
    }

    public long offer(DirectBuffer buffer, int offset, int length) {
      long startTime = System.nanoTime();
      offerCallCount.incrementAndGet();

      // 模拟 Aeron cluster offer 的处理时间
      simulateAeronClusterProcessing();

      long endTime = System.nanoTime();
      totalLatency.addAndGet(endTime - startTime);

      // 返回成功的 position
      return startTime; // 使用 startTime 作为 position
    }

    private void simulateAeronClusterProcessing() {
      // 模拟网络传输延迟（2-10微秒）
      long networkLatency = 2000 + ThreadLocalRandom.current().nextLong(8000);
      busyWait(networkLatency);
    }

    private void busyWait(long nanos) {
      long start = System.nanoTime();
      while (System.nanoTime() - start < nanos) {
        // 忙等待
      }
    }
  }

  /** 手动实现的 ExclusivePublication，替代 Mockito mock */
  private static class ManualExclusivePublication {
    private final AtomicLong publicationCallCount;
    private final AtomicLong totalLatency;

    public ManualExclusivePublication(AtomicLong publicationCallCount, AtomicLong totalLatency) {
      this.publicationCallCount = publicationCallCount;
      this.totalLatency = totalLatency;
    }

    public long offer(DirectBuffer buffer, int offset, int length) {
      long startTime = System.nanoTime();
      publicationCallCount.incrementAndGet();

      // 模拟本地发布延迟（0.5-3微秒）
      long localLatency = 500 + ThreadLocalRandom.current().nextLong(2500);
      busyWait(localLatency);

      long endTime = System.nanoTime();
      totalLatency.addAndGet(endTime - startTime);

      // 返回成功的 position
      return startTime; // 使用 startTime 作为 position
    }

    private void busyWait(long nanos) {
      long start = System.nanoTime();
      while (System.nanoTime() - start < nanos) {
        // 忙等待
      }
    }
  }

  /** 手动实现的 OrderService，替代 Mockito mock */
  private static class ManualOrderService {
    private final ManualClientManager clientManager;
    private final OrderEventFullChainBenchmark benchmark;

    public ManualOrderService(
        ManualClientManager clientManager, OrderEventFullChainBenchmark benchmark) {
      this.clientManager = clientManager;
      this.benchmark = benchmark;
    }

    public void placeOrder(Order order) {
      // 模拟 OrderService 内部调用 clientManager.send
      benchmark.simulateOrderServiceProcessing(order);
    }

    public void cancelOrder(PsOrder order) {
      // 模拟 OrderService 内部调用 clientManager.send
      benchmark.simulateCancelOrderProcessing(order);
    }
  }

  /** 手动实现的 ClientManager，替代 Mockito mock */
  private static class ManualClientManager {
    private final ManualAeronCluster aeronCluster;
    private final ManualExclusivePublication publication;
    private final AtomicLong totalLatency;
    private final AtomicLong totalSerializationTime;
    private final OrderEventFullChainBenchmark benchmark;

    public ManualClientManager(
        ManualAeronCluster aeronCluster,
        ManualExclusivePublication publication,
        AtomicLong totalLatency,
        AtomicLong totalSerializationTime,
        OrderEventFullChainBenchmark benchmark) {
      this.aeronCluster = aeronCluster;
      this.publication = publication;
      this.totalLatency = totalLatency;
      this.totalSerializationTime = totalSerializationTime;
      this.benchmark = benchmark;
    }

    public Object send(AeronMessage message) {
      return benchmark.simulateClientManagerSend(message);
    }

    public Object sendToOwn(AeronMessage message) {
      return benchmark.simulateClientManagerSendToOwn(message);
    }
  }
}
